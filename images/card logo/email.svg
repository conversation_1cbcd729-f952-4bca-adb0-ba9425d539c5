<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12_372)">
<rect x="2" y="2" width="50" height="50" rx="25" fill="white" shape-rendering="crispEdges"/>
<g clip-path="url(#clip0_12_372)">
<path d="M39.9615 16.2231H32.1369C31.878 16.2231 31.6681 16.4329 31.6681 16.6918C31.6681 16.9507 31.878 17.1606 32.1369 17.1606H39.9615C40.5363 17.1606 41.0039 17.6281 41.0039 18.2029V34.9056H12.9961V18.2029C12.9961 17.6281 13.4637 17.1606 14.0385 17.1606H17.2383C17.4972 17.1606 17.7071 16.9507 17.7071 16.6918C17.7071 16.4329 17.4972 16.2231 17.2383 16.2231H14.0384C12.9468 16.2231 12.0586 17.1112 12.0586 18.2029V36.3934C12.0586 37.485 12.9468 38.3732 14.0385 38.3732H20.7302L19.2959 40.8346C19.1674 41.0552 19.1657 41.3175 19.2915 41.5364C19.4177 41.756 19.6523 41.8924 19.9038 41.8924H34.0963C34.3478 41.8924 34.5824 41.756 34.7086 41.5364C34.8344 41.3176 34.8328 41.0552 34.7043 40.8347L33.2699 38.3732H39.9615C41.0532 38.3732 41.9414 37.485 41.9414 36.3934V18.2029C41.9414 17.1112 41.0532 16.2231 39.9615 16.2231ZM33.6892 40.9549H20.3108L21.8153 38.3732H32.1848L33.6892 40.9549ZM39.9615 37.4357H14.0384C13.4637 37.4357 12.996 36.9681 12.996 36.3934V35.843H41.0038V36.3934C41.0039 36.9681 40.5363 37.4357 39.9615 37.4357ZM22.4535 30.9785C22.4535 31.2374 22.2436 31.4472 21.9847 31.4472H15.3582C15.0994 31.4472 14.8895 31.2374 14.8895 30.9785C14.8895 30.7196 15.0994 30.5097 15.3582 30.5097H21.9847C22.2436 30.5097 22.4535 30.7196 22.4535 30.9785ZM22.4535 29.0461C22.4535 29.305 22.2436 29.5149 21.9847 29.5149H15.3582C15.0994 29.5149 14.8895 29.305 14.8895 29.0461C14.8895 28.7873 15.0994 28.5774 15.3582 28.5774H21.9847C22.2436 28.5774 22.4535 28.7873 22.4535 29.0461ZM22.4535 32.9108C22.4535 33.1697 22.2436 33.3795 21.9847 33.3795H15.3582C15.0994 33.3795 14.8895 33.1697 14.8895 32.9108C14.8895 32.6519 15.0994 32.442 15.3582 32.442H21.9847C22.2436 32.442 22.4535 32.6519 22.4535 32.9108ZM16.0905 18.7748C15.5947 19.0611 15.3582 19.6258 15.4633 20.1586C14.5095 20.8009 14.2011 22.0851 14.7846 23.0957C15.0789 23.6054 15.5557 23.9704 16.1273 24.1235C16.3189 24.1749 16.5131 24.2003 16.706 24.2003C17.0382 24.2003 17.3661 24.1247 17.6687 23.9766C17.817 24.1063 17.9934 24.2024 18.1896 24.2549C18.3023 24.2852 18.4167 24.3001 18.5303 24.3001C18.6968 24.3001 18.8614 24.2677 19.0168 24.2045L20.5792 26.9107C20.7405 27.1901 21.0343 27.3465 21.3358 27.3465C21.4836 27.3465 21.6333 27.3089 21.7703 27.2298L22.4961 26.8108C22.6976 26.6945 22.8418 26.5064 22.9021 26.2813C22.9625 26.056 22.9316 25.8211 22.8154 25.6197L21.2711 22.9449L26.1838 21.8607C26.4491 22.1028 26.7952 22.2345 27.1484 22.2345C27.3904 22.2345 27.6355 22.173 27.8596 22.0435C28.1888 21.8535 28.4245 21.5456 28.5234 21.1767C28.6223 20.8077 28.572 20.4232 28.3819 20.094L27.4073 18.4058C27.8469 17.9732 28.1061 17.3755 28.1061 16.7466C28.1061 15.4648 27.0633 14.4221 25.7815 14.4221C25.5696 14.4221 25.3596 14.4518 25.1571 14.5082L24.1829 12.8209C23.9927 12.4916 23.6849 12.2558 23.316 12.1571C22.947 12.0582 22.5626 12.1085 22.2333 12.2986C21.9041 12.4886 21.6684 12.7964 21.5695 13.1654C21.5092 13.3905 21.5047 13.6213 21.5533 13.8418L18.106 17.6111L16.0905 18.7748ZM25.6235 21.0243L21.2578 21.9877L18.9809 18.0438L21.998 14.7448L25.6235 21.0243ZM16.3699 23.218C16.0402 23.1297 15.7655 22.9198 15.5965 22.627C15.2838 22.0854 15.4165 21.408 15.879 21.0183L17.131 23.1868C16.889 23.2742 16.6257 23.2865 16.3699 23.218ZM18.4322 23.3494C18.337 23.3239 18.2578 23.2633 18.2089 23.1788L16.4462 20.1256C16.4461 20.1256 16.4461 20.1255 16.446 20.1254C16.446 20.1253 16.4459 20.1253 16.4459 20.1252L16.4248 20.0886C16.3235 19.9132 16.3839 19.688 16.5593 19.5867L18.2316 18.6212L20.3832 22.3479L18.7109 23.3134C18.6265 23.3621 18.5276 23.3749 18.4322 23.3494ZM21.3585 26.3851L19.8352 23.7467L20.4475 23.3931L21.9708 26.0315L21.3585 26.3851ZM25.7816 15.3595C26.5465 15.3595 27.1687 15.9817 27.1687 16.7466C27.1687 17.0372 27.0756 17.3164 26.9121 17.5477L25.6526 15.3662C25.6954 15.3623 25.7384 15.3595 25.7816 15.3595ZM22.7022 13.1104C22.7791 13.066 22.8632 13.0449 22.9462 13.0449C23.1155 13.0449 23.2805 13.1327 23.3711 13.2896L27.5702 20.5627C27.635 20.6751 27.652 20.807 27.618 20.934C27.5839 21.0611 27.5033 21.1668 27.3909 21.2316C27.2786 21.2965 27.1468 21.3134 27.0197 21.2794C26.8927 21.2454 26.787 21.1647 26.7221 21.0524L22.523 13.7792C22.388 13.5454 22.4684 13.2453 22.7022 13.1104ZM28.4788 14.6482L29.5414 14.0346C29.7657 13.9053 30.0523 13.9821 30.1818 14.2062C30.3112 14.4304 30.2344 14.7171 30.0102 14.8465L28.9476 15.46C28.8738 15.5027 28.7932 15.5229 28.7136 15.5229C28.5516 15.5229 28.3941 15.4388 28.3072 15.2885C28.1779 15.0642 28.2547 14.7776 28.4788 14.6482ZM27.1084 13.6208L27.7219 12.5582C27.8513 12.3339 28.138 12.2572 28.3622 12.3865C28.5864 12.5159 28.6632 12.8026 28.5339 13.0268L27.9203 14.0895C27.8336 14.2399 27.6759 14.324 27.5139 14.324C27.4344 14.324 27.3538 14.3037 27.28 14.2612C27.0558 14.1317 26.979 13.845 27.1084 13.6208ZM29.152 16.2231H30.379C30.6379 16.2231 30.8477 16.4329 30.8477 16.6918C30.8477 16.9507 30.6379 17.1606 30.379 17.1606H29.152C28.8932 17.1606 28.6833 16.9507 28.6833 16.6918C28.6833 16.4329 28.8932 16.2231 29.152 16.2231ZM24.5421 24.531V32.1118C24.5421 32.8769 25.1646 33.4993 25.9296 33.4993L38.2102 33.4992C38.9752 33.4992 39.5977 32.8768 39.5977 32.1117V24.531C39.5977 23.7659 38.9752 23.1435 38.2102 23.1435H25.9296C25.1646 23.1435 24.5421 23.7659 24.5421 24.531ZM29.5675 27.8185L25.4796 31.9V24.5814L29.5675 27.8185ZM30.3074 28.4045L31.2086 29.1181C31.4621 29.3189 31.766 29.4194 32.0699 29.4194C32.3738 29.4194 32.6777 29.3189 32.9313 29.1181L33.8324 28.4045L37.9961 32.5617L26.1437 32.5617L30.3074 28.4045ZM34.5723 27.8185L38.6602 24.5814V31.9001L34.5723 27.8185ZM32.3493 28.3831C32.1821 28.5156 31.9576 28.5156 31.7905 28.3832L26.3578 24.081H37.7819L32.3493 28.3831Z" fill="black"/>
</g>
</g>
<defs>
<filter id="filter0_d_12_372" x="0" y="0" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12_372"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12_372" result="shape"/>
</filter>
<clipPath id="clip0_12_372">
<rect width="30" height="30" fill="white" transform="translate(12 12)"/>
</clipPath>
</defs>
</svg>
