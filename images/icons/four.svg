<svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12_925)">
<rect x="2.66669" y="3" width="60" height="60" rx="30" fill="#43197D" shape-rendering="crispEdges"/>
<g clip-path="url(#clip0_12_925)">
<path d="M52.6659 43.7086V51.7742C52.6659 52.0687 52.4269 52.3078 52.1323 52.3078H42.0276V43.1727H52.1323C52.4269 43.1727 52.6659 43.4141 52.6659 43.7086ZM12.6667 43.7086V51.7742C12.6667 52.0687 12.9081 52.3078 13.2003 52.3078H23.305V43.1727H13.2003C12.9081 43.1727 12.6667 43.4141 12.6667 43.7086ZM29.0987 16.3539C29.0987 16.243 29.1456 16.1437 29.2183 16.068C29.2917 15.9945 29.3933 15.9484 29.5042 15.9484H35.8292C35.9401 15.9484 36.0417 15.9953 36.1151 16.068C36.1901 16.143 36.2347 16.243 36.2347 16.3539V17.4375H38.4917V16.3539C38.4917 15.6211 38.1925 14.9562 37.712 14.4734C37.2292 13.9906 36.5628 13.6914 35.83 13.6914H29.505C28.7722 13.6914 28.1058 13.9906 27.6245 14.4734C27.1417 14.9562 26.8425 15.6203 26.8425 16.3539V17.4375H29.0995V16.3539H29.0987ZM24.7269 25.4961C24.6823 25.4914 24.6386 25.4852 24.5964 25.4758V30.9328C24.5964 31.4687 25.0503 31.907 25.6081 31.907H27.112C26.5144 30.9234 26.1706 29.7703 26.1706 28.5344C26.1706 27.5398 26.394 26.5969 26.7948 25.7531L24.7269 25.4961ZM38.5394 25.7531C38.9378 26.5969 39.1636 27.5406 39.1636 28.5344C39.1636 29.7703 38.8183 30.9242 38.2198 31.907H39.7237C40.2815 31.907 40.7354 31.4687 40.7354 30.9328V25.4758C40.6933 25.4844 40.6487 25.4914 40.605 25.4961L38.5394 25.7531ZM40.3284 18.7258H25.0065C24.4683 18.7258 24.0323 19.1641 24.0323 19.7V23.25C24.0323 23.7437 24.3956 24.1555 24.887 24.2156L27.5401 24.5453C28.7292 23.0195 30.5831 22.0383 32.6667 22.0383C34.7503 22.0383 36.6042 23.0195 37.7933 24.5453L40.4487 24.2156C40.9378 24.1555 41.3011 23.7437 41.3011 23.25V19.7008C41.3011 19.1648 40.8644 18.7258 40.3284 18.7258ZM32.6667 29.5617C33.1519 29.5617 33.5901 29.3672 33.9065 29.0477C34.2229 28.7312 34.4206 28.2922 34.4206 27.8078C34.4206 27.325 34.2237 26.8867 33.9065 26.568C33.5901 26.2516 33.1511 26.0539 32.6667 26.0539C32.1815 26.0539 31.7433 26.2508 31.4269 26.568C31.1104 26.8867 30.9128 27.325 30.9128 27.8078C30.9128 28.293 31.1097 28.7312 31.4269 29.0477C31.7433 29.3672 32.1815 29.5617 32.6667 29.5617ZM30.1261 31.232C29.8823 31.5484 29.7167 31.9273 29.6636 32.3391L29.6104 32.7484C30.4675 33.3727 31.5237 33.7406 32.6667 33.7406C33.8097 33.7406 34.8659 33.3727 35.7229 32.7484L35.6698 32.3391C35.6167 31.9273 35.4526 31.5484 35.2089 31.2344C34.9589 30.9109 34.6284 30.6539 34.2479 30.4992L34.1612 30.4617C33.7183 30.7117 33.2089 30.8539 32.6667 30.8539C32.1245 30.8539 31.6151 30.7125 31.1737 30.4617L31.0894 30.4969C30.7089 30.6516 30.3769 30.9086 30.1261 31.232ZM36.2253 30.4453C36.5003 30.8016 36.7104 31.2094 36.8362 31.6523C37.487 30.782 37.8729 29.7039 37.8729 28.5344C37.8729 25.6602 35.5409 23.3281 32.6667 23.3281C29.7925 23.3281 27.4604 25.6602 27.4604 28.5344C27.4604 29.7039 27.8456 30.782 28.4972 31.6523C28.6237 31.2094 28.8339 30.8 29.1104 30.4453C29.394 30.0758 29.7503 29.7633 30.1534 29.5266C29.819 29.0375 29.6245 28.4461 29.6245 27.8078C29.6245 26.9687 29.9636 26.207 30.5151 25.6555C31.0667 25.1062 31.8284 24.7648 32.6675 24.7648C33.5089 24.7648 34.2683 25.1062 34.8198 25.6555C35.3714 26.207 35.712 26.9687 35.712 27.8078C35.712 28.4453 35.5151 29.0391 35.1808 29.5266C35.5854 29.7633 35.9417 30.0781 36.2253 30.4453ZM39.8058 36.4023H25.5269C25.0151 36.4023 24.5964 36.8211 24.5964 37.3344V52.307H40.7362V37.3352C40.7362 36.8211 40.3175 36.4023 39.8058 36.4023ZM13.1894 25.6664H15.005C15.2933 25.6664 15.5253 25.8992 15.5253 26.1867V38.2297C15.5253 38.4664 15.7198 38.6594 15.9573 38.6594H18.3737C18.6104 38.6594 18.8058 38.4648 18.8058 38.2297V26.1867C18.8058 25.8984 19.0386 25.6664 19.3237 25.6664H21.1417C21.3565 25.6664 21.5401 25.5422 21.6222 25.3453C21.7042 25.1461 21.662 24.9289 21.5089 24.7781L17.5323 20.8008C17.3308 20.5969 17.0011 20.5969 16.7972 20.8008L12.819 24.7781C12.6683 24.9289 12.6245 25.1461 12.7081 25.3453C12.7909 25.5422 12.9745 25.6664 13.1894 25.6664ZM44.1909 25.6664H46.0089C46.2948 25.6664 46.5292 25.8992 46.5292 26.1867V38.2297C46.5292 38.4664 46.7222 38.6594 46.9589 38.6594H49.3769C49.6136 38.6594 49.8065 38.4648 49.8065 38.2297V26.1867C49.8065 25.8984 50.0409 25.6664 50.3269 25.6664H52.1448C52.3597 25.6664 52.5433 25.5422 52.6253 25.3453C52.7073 25.1461 52.6628 24.9289 52.512 24.7781L48.5347 20.8008C48.3308 20.5969 48.0011 20.5969 47.7995 20.8008L43.8222 24.7781C43.669 24.9289 43.6276 25.1461 43.7089 25.3453C43.7925 25.5422 43.9761 25.6664 44.1909 25.6664Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_12_925" x="0.266687" y="0.6" width="64.8" height="64.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12_925"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12_925" result="shape"/>
</filter>
<clipPath id="clip0_12_925">
<rect width="40" height="40" fill="white" transform="translate(12.6667 13)"/>
</clipPath>
</defs>
</svg>
