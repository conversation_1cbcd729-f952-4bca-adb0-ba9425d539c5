<svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12_584)">
<rect x="3.00003" y="3" width="60" height="60" rx="30" fill="#43197D" shape-rendering="crispEdges"/>
<g clip-path="url(#clip0_12_584)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.5711 25.0149C49.6561 25.0149 52.157 22.514 52.157 19.4289C52.157 16.3438 49.6561 13.8431 46.5711 13.8431C43.486 13.8431 40.9851 16.344 40.9851 19.429C40.9851 22.514 43.4861 25.0149 46.5711 25.0149V25.0149ZM46.5711 15.4942C47.4825 15.4942 48.2212 16.2329 48.2212 17.1443C48.2212 18.0557 47.4825 18.7945 46.5711 18.7945C45.6597 18.7945 44.9209 18.0556 44.9209 17.1443C44.9209 16.233 45.6597 15.4942 46.5711 15.4942ZM46.5711 19.2661C48.306 19.2661 49.7654 20.4433 50.1954 22.0424C49.3839 23.1659 48.063 23.8971 46.5711 23.8971C45.0791 23.8971 43.7582 23.1659 42.9468 22.0424C43.3768 20.4434 44.8361 19.2662 46.571 19.2662L46.5711 19.2661ZM46.5711 40.9852C43.4861 40.9852 40.9851 43.4861 40.9851 46.5711C40.9851 49.6561 43.4861 52.157 46.5711 52.157C49.6561 52.157 52.157 49.6562 52.157 46.5711C52.157 43.486 49.6561 40.9852 46.5711 40.9852ZM46.5711 42.6363C47.4825 42.6363 48.2212 43.3752 48.2212 44.2865C48.2212 45.1978 47.4825 45.9367 46.5711 45.9367C45.6597 45.9367 44.9209 45.1978 44.9209 44.2865C44.9209 43.3752 45.6597 42.6363 46.5711 42.6363ZM46.5711 51.0392C45.0791 51.0392 43.7582 50.308 42.9468 49.1845C43.3768 47.5855 44.8361 46.4082 46.571 46.4082C48.3058 46.4082 49.7653 47.5854 50.1953 49.1845C49.3838 50.308 48.0629 51.0392 46.571 51.0392H46.5711ZM19.4289 40.9852C16.3439 40.9852 13.843 43.4861 13.843 46.5711C13.843 49.6561 16.3439 52.157 19.4289 52.157C22.5139 52.157 25.0149 49.6562 25.0149 46.5711C25.0149 43.486 22.514 40.9852 19.4289 40.9852ZM19.4289 42.6363C20.3403 42.6363 21.0791 43.3752 21.0791 44.2865C21.0791 45.1978 20.3403 45.9367 19.4289 45.9367C18.5176 45.9367 17.7788 45.1978 17.7788 44.2865C17.7788 43.3752 18.5175 42.6363 19.4289 42.6363ZM19.4289 51.0392C17.937 51.0392 16.6161 50.308 15.8047 49.1845C16.2347 47.5855 17.694 46.4082 19.4289 46.4082C21.1637 46.4082 22.6232 47.5854 23.0532 49.1845C22.2417 50.308 20.9209 51.0392 19.4289 51.0392ZM19.4289 25.0149C22.514 25.0149 25.0149 22.5141 25.0149 19.429C25.0149 16.3439 22.514 13.8431 19.4289 13.8431C16.3439 13.8431 13.843 16.3439 13.843 19.4289C13.843 22.5139 16.3439 25.0149 19.4289 25.0149V25.0149ZM19.4289 15.4942C20.3403 15.4942 21.0791 16.233 21.0791 17.1444C21.0791 18.0558 20.3403 18.7945 19.4289 18.7945C18.5176 18.7945 17.7788 18.0557 17.7788 17.1444C17.7788 16.2331 18.5175 15.4942 19.4289 15.4942ZM19.4289 19.2662C21.1639 19.2662 22.6233 20.4434 23.0532 22.0425C22.2418 23.166 20.9209 23.8972 19.4289 23.8972C17.937 23.8972 16.6161 23.166 15.8047 22.0425C16.2347 20.4435 17.694 19.2663 19.4289 19.2663L19.4289 19.2662ZM34.8938 41.0696V42.6029H36.8567C37.6371 42.6029 38.2734 43.2394 38.2734 44.0196V44.3635H27.7265V44.0196C27.7265 43.2393 28.3629 42.6029 29.1432 42.6029H31.1062V41.0696H34.8938ZM41.5896 39.9759H24.4105C24.0503 39.9759 23.7554 39.6811 23.7554 39.3208V27.6924C23.7554 27.3321 24.0502 27.0373 24.4105 27.0373H26.821C26.3546 27.9949 26.131 29.0209 26.1332 30.1889C26.1403 33.9035 29.2437 36.0804 32.7284 36.0804C36.2131 36.0804 39.3166 33.9035 39.3236 30.1889C39.3258 29.0209 39.1023 27.9949 38.6358 27.0373H41.5895C41.9497 27.0373 42.2445 27.3322 42.2445 27.6924V39.3208C42.2445 39.681 41.9497 39.9759 41.5895 39.9759H41.5896ZM39.4353 50.3258C39.5321 50.612 39.378 50.9219 39.0922 51.0187C37.1352 51.6807 35.0852 52.0159 33.0001 52.0159C31.1893 52.0159 29.4065 51.7635 27.6872 51.2635L28.0664 51.8401C28.2321 52.0931 28.162 52.4317 27.9097 52.5977C27.8171 52.6588 27.7128 52.6877 27.61 52.6877C27.4318 52.6877 27.2575 52.6011 27.1525 52.4418L25.9018 50.5394C25.8107 50.4008 25.7874 50.2284 25.8386 50.0706C25.8897 49.9127 26.0099 49.7863 26.165 49.7275L28.2711 48.9307C28.5536 48.8238 28.8691 48.9657 28.9761 49.2477C29.083 49.5306 28.9407 49.8461 28.6582 49.9535L27.98 50.2095C29.6042 50.6828 31.2886 50.922 33 50.922C34.9657 50.922 36.8975 50.6064 38.7423 49.9832C39.0281 49.8853 39.3384 50.0393 39.4352 50.3259L39.4353 50.3258ZM13.4025 27.9103C13.2365 27.6581 13.3065 27.3187 13.5588 27.1523L15.4608 25.9017C15.5994 25.8109 15.7718 25.7876 15.93 25.8392C16.0879 25.89 16.214 26.0101 16.2727 26.1649L17.0699 28.271C17.1768 28.5538 17.0345 28.8693 16.7521 28.9767C16.4697 29.0828 16.1541 28.9405 16.0468 28.6585L15.7904 27.9802C15.3174 29.604 15.0782 31.2884 15.0782 32.9999C15.0782 34.9656 15.3942 36.8977 16.0174 38.7421C16.1142 39.0279 15.9605 39.339 15.6747 39.435C15.6168 39.4545 15.5576 39.464 15.4993 39.464C15.2714 39.464 15.0587 39.3202 14.9814 39.0924C14.3198 37.1354 13.9843 35.0849 13.9843 32.9999C13.9843 31.1891 14.237 29.4064 14.7367 27.6877L14.1598 28.0666C13.9075 28.2323 13.5682 28.1626 13.4025 27.9103ZM52.4407 38.8477L50.5391 40.0986C50.4491 40.1577 50.3448 40.1882 50.2382 40.1882C50.1822 40.1882 50.1253 40.1795 50.0703 40.1619C49.9118 40.1103 49.786 39.9898 49.7276 39.8354L48.9297 37.7289C48.8235 37.4461 48.9658 37.1306 49.2479 37.0236C49.5307 36.9174 49.8462 37.0594 49.9532 37.3414L50.2096 38.0196C50.6829 36.3963 50.9221 34.7119 50.9221 33C50.9221 31.0343 50.6057 29.1026 49.9822 27.2582C49.8861 26.972 50.039 26.6617 50.3252 26.5649C50.611 26.4674 50.9221 26.6214 51.0188 26.908C51.6804 28.8649 52.0156 30.915 52.0156 33C52.0156 34.811 51.7633 36.594 51.2636 38.3134L51.8397 37.9338C52.0924 37.7681 52.4313 37.8374 52.5978 38.0908C52.7635 38.3431 52.6938 38.682 52.4407 38.8477ZM26.5647 15.6742C26.4679 15.3885 26.6215 15.0781 26.9077 14.9813C28.865 14.3197 30.9147 13.9846 33.0001 13.9846C34.8111 13.9846 36.5937 14.2369 38.3127 14.737L37.9339 14.1605C37.7682 13.9078 37.8375 13.5689 38.0898 13.4024C38.3421 13.2367 38.6814 13.3064 38.8479 13.5587L40.0977 15.4611C40.1892 15.5993 40.2125 15.7721 40.161 15.9299C40.1105 16.0877 39.99 16.2142 39.8345 16.2726L37.7291 17.0706C37.665 17.0939 37.5992 17.1056 37.5352 17.1056C37.3142 17.1056 37.1063 16.9711 37.0234 16.7524C36.9164 16.4695 37.0596 16.154 37.3416 16.047L38.0198 15.7906C36.3961 15.3173 34.7117 15.0781 33.0002 15.0781C31.0342 15.0781 29.1024 15.3949 27.258 16.0181C26.9718 16.1141 26.6615 15.9612 26.5647 15.6742H26.5647ZM31.025 21.3691C30.5028 20.5631 29.9493 19.6003 30.4057 19.3181C31.0868 18.8968 31.6014 19.9234 32.0166 19.3342C32.2624 18.9852 32.4954 18.8108 32.7284 18.8108C32.9614 18.8108 33.1944 18.9852 33.4402 19.3342C33.8554 19.9234 34.37 18.8968 35.0511 19.3181C35.5076 19.6004 34.9541 20.5631 34.4318 21.3691H31.025H31.025ZM32.7285 34.9867C35.4762 34.9867 38.2239 33.3867 38.23 30.1868C38.2368 26.6314 35.7984 25.4917 34.487 23.5917H34.8197C35.1302 23.5917 35.3842 23.3377 35.3842 23.0272C35.3842 22.7167 35.1302 22.4627 34.8197 22.4627H30.6375C30.327 22.4627 30.073 22.7167 30.073 23.0272C30.073 23.3377 30.327 23.5917 30.6375 23.5917H30.9701C29.6586 25.4917 27.2203 26.6314 27.227 30.1868C27.2331 33.3867 29.9807 34.9867 32.7285 34.9867ZM32.7285 26.4031C34.5647 26.4031 36.0533 27.8917 36.0533 29.728C36.0533 31.5642 34.5647 33.0528 32.7285 33.0528C30.8922 33.0528 29.4036 31.5642 29.4036 29.728C29.4036 27.8917 30.8922 26.4031 32.7285 26.4031ZM30.7269 29.632C30.5771 29.3713 30.667 29.0385 30.9277 28.8886C31.1884 28.7388 31.5213 28.8287 31.6711 29.0894L32.1575 29.9317L33.8714 28.2178C34.0849 28.0043 34.4311 28.0043 34.6447 28.2178C34.8582 28.4313 34.8582 28.7776 34.6447 28.9911L32.4286 31.2072C32.1754 31.4603 31.7479 31.4017 31.5698 31.0919L30.7269 29.632Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_12_584" x="0.60003" y="0.6" width="64.8" height="64.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12_584"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12_584" result="shape"/>
</filter>
<clipPath id="clip0_12_584">
<rect width="40" height="40" fill="white" transform="translate(13 13)"/>
</clipPath>
</defs>
</svg>
